{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "release-pod",
  "main": "./src/server/index.tsx",
  "assets": {
    "directory": "dist"
  },
  "env": {
    "dev": {
      "vars": {
        "ENVIRONMENT": "dev"
      }
    },
    "prod": {
      "vars": {
        "ENVIRONMENT": "production"
      }
    }
  },
  "compatibility_date": "2025-09-26",
  "compatibility_flags": ["nodejs_compat"],
  "kv_namespaces": [
    {
      "binding": "podcasts",
      "id": "f727bee63bd0458c9897f3c592688067"
    }
  ]
  // "vars": {
  //   "MY_VAR": "my-variable"
  // },
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  //   }
  // ],
  // "r2_buckets": [
  //   {
  //     "binding": "MY_BUCKET",
  //     "bucket_name": "my-bucket"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "MY_DB",
  //     "database_name": "my-database",
  //     "database_id": ""
  //   }
  // ],
  // "ai": {
  //   "binding": "AI"
  // },
  // "observability": {
  //   "enabled": true,
  //   "head_sampling_rate": 1
  // }
}
