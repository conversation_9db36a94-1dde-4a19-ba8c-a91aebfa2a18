# Documentation

## Entities

- dialog
- podcast

### dialog

- source_url str: url of the post
- content str: generated dialog
- podcast_key str: key to retrieve the podcast

### podcast

- dialog_key str: key to retrieve the dialog
- audio bytes: audio data

### kv keys

- post:post_key:dialog -> dialog
- post:post_key:podcast -> podcast

## State Diagram

```mermaid
stateDiagram-v2
    [*] --> idle
    idle --> extracting_content: submit_link
    extracting_content --> content_error: error
    content_error --> extracting_content: retry
    extracting_content --> generating_dialog: generate_dialog

    generating_dialog --> dialog_error: error
    dialog_error --> generating_dialog: retry

    generating_dialog --> dialog_generated: dialog_ready
    dialog_generated --> dialog_saved: save_dialog

    dialog_saved --> generating_podcast: generate_podcast
    generating_podcast --> podcast_error: error
    podcast_error --> generating_podcast: retry

    generating_podcast --> podcast_generated: podcast_ready
    podcast_generated --> save_podcast: save_podcast

    save_podcast --> idle: done
```

## Sequence Diagram

```mermaid
sequenceDiagram
    participant client as Client
    participant api as API
    participant kv as KV
    participant elevenlabs as ElevenLabs
    participant llm as LLM

    client ->> api: Submit link
    api ->> api: Extract content
    api ->> llm: Generate dialog
    llm ->> api: Return dialog
    api ->> kv: Save dialog

    api ->> elevenlabs: Generate podcast
    elevenlabs ->> api : Return podcast
    api ->> kv: Save podcast

    client ->> api: Request dialog
    api ->> kv: Get dialog
    kv ->> api: Return dialog
    api ->> client: Return dialog

    client ->> api: Request podcast
    api ->> kv: Get podcast
    kv ->> api: Return podcast
    api ->> client: Return podcast

```
