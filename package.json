{"name": "release-pod", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode client", "preview": "wrangler --env prod", "deploy": "$npm_execpath run build && wrangler deploy --minify --env prod", "dev:old": "wrangler dev", "deploy:old": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "lint": "biome check", "lint:fix": "npm run lint -- --write"}, "dependencies": {"@elevenlabs/elevenlabs-js": "^2.16.0", "@extractus/feed-extractor": "^7.1.7", "@hono/node-server": "^1.19.4", "@hono/zod-validator": "^0.7.3", "@mozilla/readability": "^0.6.0", "dompurify": "^3.2.7", "hono": "^4.9.8", "linkedom": "^0.18.12", "zod": "^4.1.11"}, "devDependencies": {"@biomejs/biome": "^2.2.4", "@cloudflare/vite-plugin": "^1.13.8", "@hono/vite-build": "^1.7.0", "@hono/vite-dev-server": "^0.21.1", "@types/node": "^20.11.17", "tsx": "^4.7.1", "typescript": "^5.8.3", "vite": "^7.1.7", "vite-plugin-ssr-hot-reload": "^0.5.0", "vite-ssr-components": "^0.5.1", "wrangler": "^4.40.2"}}