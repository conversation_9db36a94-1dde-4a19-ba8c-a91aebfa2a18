{"$schema": "https://biomejs.dev/schemas/2.2.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/.wrangler", "!**/worker-configuration.d.ts"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off"}, "a11y": {"useSemanticElements": "off", "noRedundantRoles": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}