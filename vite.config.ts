import { cloudflare } from "@cloudflare/vite-plugin";
import { defineConfig } from "vite";
import ssrPlugin from "vite-ssr-components/plugin";

export default defineConfig(({ mode, command }) => {
  if (command === "build") {
    if (mode === "client") {
      return {
        esbuild: {
          jsxImportSource: "hono/jsx/dom",
        },
        build: {
          rollupOptions: {
            input: ["./src/client/main.tsx"],
            output: {
              entryFileNames: "static/client/main.js",
            },
          },
          copyPublicDir: true,
        },
      };
    } else {
      return {};
    }
  } else {
    return {
      plugins: [cloudflare(), ssrPlugin()],
    };
  }
});
