import { Hono } from "hono";
import { jsx<PERSON><PERSON><PERSON> } from "hono/jsx-renderer";
import { Script, ViteClient } from "vite-ssr-components/hono";
import type { AppEnv } from "../types";

export function createApp() {
  const app = new Hono<AppEnv>();

  app.use(
    "*",
    jsxRenderer(({ children }) => {
      return (
        <html lang="en">
          <head>
            <meta
              name="viewport"
              content="width=device-width, initial-scale=1"
            ></meta>
            <link
              rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/@picocss/pico@2/css/pico.orange.min.css"
            />
            <ViteClient />
            <Script src="/src/client/main.tsx" />
          </head>
          <body>{children}</body>
        </html>
      );
    }),
  );

  app.notFound((c) => {
    return c.render(
      <main class="container">
        <h1>Not Found</h1>
        <p>{c.req.path}</p>
      </main>,
    );
  });

  app.onError((error, c) => {
    return c.render(
      <main class="container">
        <h1>Internal Server Error</h1>
        <p>{error.message}</p>
      </main>,
    );
  });

  return app;
}
