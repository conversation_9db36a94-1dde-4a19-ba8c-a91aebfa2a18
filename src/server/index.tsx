import { z<PERSON><PERSON><PERSON><PERSON> } from "@hono/zod-validator";
import z from "zod";
import { Layout } from "../components/layout";
import { ServerPodcastGeneratorCard } from "../components/podcast-generator-card";
import type { Dialog } from "../types";
import api from "./api";
import apiV1 from "./api/v1";
import { createApp } from "./create-app";

const app = createApp();

app.get("/", (c) => {
  return c.render(
    <Layout>
      <main>
        <ServerPodcastGeneratorCard />
      </main>
    </Layout>,
  );
});

app.get("/dialog", async (c) => {
  const dialog = await c.env.podcasts.get<Dialog>("dialog", { type: "json" });

  if (!dialog) {
    console.log({ message: "dialog not found" });
    c.status(404);
    return c.redirect("/");
  }

  console.log({ message: "dialog rendered" });
  return c.render(
    <Layout>
      <ServerPodcastGeneratorCard dialogAction="/api/dialog-podcast" />
      <article>
        <header>Dialog</header>
        <div>
          {dialog.content.split("\n").map((line) => (
            <p key={line}>{line}</p>
          ))}
        </div>
      </article>
    </Layout>,
  );
});

app.get("/about", (c) => {
  return c.render(
    <Layout>
      <main>
        <article>
          <header>About</header>
          <p>MBL</p>
        </article>
      </main>
    </Layout>,
  );
});

app.route("/api", api);
const apiV1Routes = app.route("/api/v1", apiV1);

app.get("/demo", (c) => {
  return c.render(<div id="root"></div>);
});

const schema = z.object({
  name: z.string(),
});

const demoApiRoutes = app.post("/demo/api", zValidator("form", schema), (c) => {
  const { name } = c.req.valid("form");
  return c.json({ name });
});

export type DemoApiRoutes = typeof demoApiRoutes;
export type ApiV1Routes = typeof apiV1Routes;

export default app;
