import { ElevenLabsClient } from "@elevenlabs/elevenlabs-js";
import { Hono } from "hono";
import { stream } from "hono/streaming";
import {
  createDialog,
  exampleDialog,
  getTextToDialogRequest,
} from "../../generate-dialog";
import type { AppEnv, Dialog } from "../../types";

const app = new Hono<AppEnv>();

const elevenlabs = new ElevenLabsClient({
  apiKey: process.env.elevenlabs_api_key as string,
});

app.post("/dialog", async (c) => {
  const body = await c.req.formData();
  const link = body.get("link") as string;
  console.log({ message: "dialog form submitted", link });
  const dialog = createDialog(link, exampleDialog);
  await c.env.podcasts.put("dialog", JSON.stringify(dialog));
  console.log({ message: "dialog saved" });

  const req = c.req.header();
  console.log({ req });
  return c.json({ status: "success" }, 200);
});

app.get("/dialog-podcast", async (c) => {
  return stream(c, async (stream) => {
    stream.onAbort(() => {
      console.log({ message: "stream aborted", success: false });
    });

    const dialog = await c.env.podcasts.get<Dialog>("dialog", { type: "json" });
    if (!dialog) {
      stream.abort();
      return;
    }
    console.log({ message: "dialog found" });

    const savedPodcastSteam = await c.env.podcasts.get(dialog.podcast_key, {
      type: "stream",
    });

    if (savedPodcastSteam) {
      console.log({
        message: "saved podcast found",
        success: true,
      });
      await stream.pipe(savedPodcastSteam);
      return;
    }

    const audioStream = await elevenlabs.textToDialogue.stream({
      inputs: getTextToDialogRequest(),
    });

    console.log({ message: "podcast generated", success: !!audioStream });

    const [streamA, streamB] = audioStream.tee();
    await Promise.all([
      c.env.podcasts.put(dialog.podcast_key, streamA).then(() => {
        console.log({ message: "podcast saved", success: true });
      }),
      stream.pipe(streamB).then(() => {
        console.log({ message: "podcast streamed", success: true });
      }),
    ]);
  });
});

export default app;
