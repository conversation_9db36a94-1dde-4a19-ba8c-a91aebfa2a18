import type { DialogueInput } from "@elevenlabs/elevenlabs-js/api";
import type { Dialog } from "./types";

export function promptFromTemplate({
  title,
  content,
}: {
  title?: string;
  content: string;
}) {
  return `
Create a highly dynamic, natural podcast conversation between two speakers about the following content. Make it feel like real people having an authentic conversation with interruptions, overlaps, and organic flow.

Title: ${title || "Article"}

Content: ${content}

HOST PERSONALITIES:
Speaker1 (Energetic & Naive):
- Extremely enthusiastic and optimistic about everything
- Easily excited by new concepts and ideas
- Asks lots of questions, sometimes obvious ones
- Uses exclamation points frequently and energetic language
- Tends to see the bright side of everything
- Sometimes misses subtleties or nuances
- Quick to get excited: "Oh wow!", "That's amazing!", "I had no idea!"

Speaker2 (Pessimistic & Arrogant):
- Skeptical and cynical about most claims
- Knows everything (or thinks they do)
- Often corrects or challenges Speaker1
- Uses condescending language and sighs frequently
- Points out flaws, problems, and downsides
- Makes sarcastic comments and eye-rolls
- Tends to be contrarian: "Actually...", "Well, obviously...", "That's not quite right..."

CRITICAL: Make this conversation feel REAL and DYNAMIC with these specific patterns:

INTERRUPTION PATTERNS:
- Use "—" (em dash) to show mid-sentence interruptions: "So I was thinking we could—" / "—test our new timing features?"
- Show speakers cutting each other off naturally
- Include overlapping thoughts and competing to speak

EMOTIONAL REACTIONS:
- Frequent emotional annotations: [laughs], [chuckles], [excited], [surprised], [skeptical], [thoughtful], [confused], [amazed]
- Show genuine reactions to what the other person says
- Include moments of realization, surprise, disagreement

CONVERSATIONAL FLOW:
- Speakers should interrupt, agree enthusiastically, or disagree
- Include side tangents and references to other topics
- Show speakers building on each other's ideas or challenging them
- Use casual language, contractions, and natural speech patterns
- Include filler words and natural hesitations occasionally

DYNAMIC EXCHANGES:
- Mix very short responses ("Wait, what?", "Exactly!", "Oh my god!") with longer explanations
- Show speakers getting excited and talking over each other
- Include moments where they both try to talk at the same time
- Reference shared knowledge or experiences they might have

EXAMPLE PERSONALITY INTERACTIONS:
- Speaker1: "Oh my god, this is incredible! So you're telling me—"
- Speaker2: "—[sighs] Obviously you missed the part where it says this barely works in practice."
- Speaker1: "Wait, but couldn't this change everything?!"
- Speaker2: "Sure, if you ignore all the obvious problems it creates. [eye roll]"
- Speaker1: "I'm so excited about this! What do you think?"
- Speaker2: "I think you're getting way too worked up over something that's been tried before and failed."

Make Speaker1 genuinely enthusiastic and sometimes adorably clueless, while Speaker2 is constantly deflating their excitement with cold realism and superiority. 

IMPORTANT: Keep the TOTAL conversation under 2500 characters to fit within API limits. Aim for 8-12 short, punchy exchanges that pack maximum impact. Focus on the most interesting or surprising aspects of the content.
  `;
}

export function createDialog(source_url: string, content: string): Dialog {
  return {
    source_url,
    content,
    podcast_key: "dialog:podcast",
  };
}

export const exampleDialog = `
Speaker 1 (River): Okay, so I just read this article on state machines for microservices, and—oh my god! It's genius! It's the solution to all that messy business logic we have!

Speaker 2 (Liam): [Sighs] You mean finite-state machines? River, that's literally one of the oldest concepts in computer science. Welcome to the party.

River: [Excited] But applying it this way! Instead of if-statements everywhere, you just define states like 'ORDER_CREATED' or 'PAYMENT_PENDING' and events trigger the change! It’s so clean!

Liam: Obviously. That’s the entire point. You’re just discovering basic design patterns now?

River: But—it says it helps avoid that 'spaghetti code' where logic is scattered everywhere. This would make our order service so much easier to understand!

Liam: [Scoffs] Oh, sure, for a trivial textbook example. Let me know how your “clean” state machine looks when you add fraud checks, inventory holds, partial shipping, and a payment gateway that times out.

River: Wait, but it would still be better than—

Liam: —than what? A distributed monolith? You're just moving the complexity. What happens when your 'SHIPPED' event fails to publish? Now you have inconsistent state. It’s not magic.

River: Okay, fine, maybe not magic, but it’s a huge improvement! The clarity you get is amazing!

Liam: [Chuckles] The clarity you get before reality hits. It’s a tool, not a savior. And from your excitement, I’m guessing you skipped the part about the complexities of state persistence and compensating transactions.

River: I… well, I still think it’s a brilliant idea!

Liam: [Muttering] Of course you do.`;

const RIVER_VOICE_ID = "SAz9YHcvj6GT2YYXdXww";

const LIAM_VOICE_ID = "TX3LPaxmHKxFdv7VOQHJ";

export const getTextToDialogRequest = (): DialogueInput[] => {
  return [
    {
      text: "Okay, so I just read this article on state machines for microservices, and—oh my god! It's genius! It's the solution to all that messy business logic we have!",
      voiceId: RIVER_VOICE_ID,
    },
    {
      text: "[Sighs] You mean finite-state machines? River, that's literally one of the oldest concepts in computer science. Welcome to the party.",
      voiceId: LIAM_VOICE_ID,
    },
    {
      text: "[Excited] But applying it this way! Instead of if-statements everywhere, you just define states like 'ORDER_CREATED' or 'PAYMENT_PENDING' and events trigger the change! It’s so clean!",
      voiceId: RIVER_VOICE_ID,
    },
    {
      text: "Obviously. That’s the entire point. You’re just discovering basic design patterns now?",
      voiceId: LIAM_VOICE_ID,
    },
    {
      text: "But—it says it helps avoid that 'spaghetti code' where logic is scattered everywhere. This would make our order service so much easier to understand!",
      voiceId: RIVER_VOICE_ID,
    },
    {
      text: "[Scoffs] Oh, sure, for a trivial textbook example. Let me know how your “clean” state machine looks when you add fraud checks, inventory holds, partial shipping, and a payment gateway that times out.",
      voiceId: LIAM_VOICE_ID,
    },
    {
      text: "Wait, but it would still be better than—",
      voiceId: RIVER_VOICE_ID,
    },
    {
      text: "—than what? A distributed monolith? You're just moving the complexity. What happens when your 'SHIPPED' event fails to publish? Now you have inconsistent state. It’s not magic.",
      voiceId: LIAM_VOICE_ID,
    },
    {
      text: "Okay, fine, maybe not magic, but it’s a huge improvement! The clarity you get is amazing!",
      voiceId: RIVER_VOICE_ID,
    },
    {
      text: "[Chuckles] The clarity you get before reality hits. It’s a tool, not a savior. And from your excitement, I’m guessing you skipped the part about the complexities of state persistence and compensating transactions.",
      voiceId: LIAM_VOICE_ID,
    },
    {
      text: "I… well, I still think it’s a brilliant idea!",
      voiceId: RIVER_VOICE_ID,
    },
    { text: "[Muttering] Of course you do.", voiceId: LIAM_VOICE_ID },
  ];
};
