import { hc } from "hono/client";
import { useState } from "hono/jsx";
import type { DemoApiRoutes } from "../../server";

const client = hc<DemoApiRoutes>("/");

export function useDemoForm() {
  const [name, setName] = useState("no name");

  const action = async (formData: FormData) => {
    const res = await client.demo.api.$post({
      form: {
        name: (formData.get("name") as string) ?? name,
      },
    });
    const data = await res.json();
    setName(data.name);
  };

  return { name, action };
}
