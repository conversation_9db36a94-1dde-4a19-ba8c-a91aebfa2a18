import type { FC } from "hono/jsx";

export const NavHeader: FC = ({ children }) => {
  return (
    <header>
      <nav>
        <ul>
          <li>
            <h1>💬 Release Pod! 🎧</h1>
          </li>
        </ul>
        <ul>{children}</ul>
      </nav>
    </header>
  );
};

export const NavLink: FC<{ href: string; children: string }> = ({
  href,
  children,
}) => {
  return (
    <li>
      <a href={href}>{children}</a>
    </li>
  );
};

export const Layout: FC = ({ children }) => {
  return (
    <div class="container">
      <NavHeader>
        <NavLink href="/">Home</NavLink>
        <NavLink href="/demo">Client Component Demo</NavLink>
        <NavLink href="/about">About</NavLink>
      </NavHeader>
      <main>{children}</main>
    </div>
  );
};
