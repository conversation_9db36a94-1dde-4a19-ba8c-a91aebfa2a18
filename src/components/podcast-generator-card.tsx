import { hc } from "hono/client";
import type { FC } from "hono/jsx";
import { useRequestContext } from "hono/jsx-renderer";
import type { ApiV1Routes } from "../server";

export const apiV1Client = hc<ApiV1Routes>("/api/v1");

interface PodcastGeneratorCardProps {
  dialogAction?: string | Function;
  podcastEndpoint?: string;
  link?: string;
  status?: "idle" | "dialog-ready";
}

function PodcastGeneratorCard({
  dialogAction = "/api/dialog",
  podcastEndpoint = "/api/dialog-podcast",
  link = "",
  status = "idle",
}: PodcastGeneratorCardProps) {
  return (
    <article>
      <header>Submit a post to generate a podcast</header>
      <form method="post" action={dialogAction}>
        <fieldset role="group">
          <input
            name="link"
            type="text"
            placeholder="Link to post"
            value={link}
            required
          />
          <input type="submit" value="Submit" />
        </fieldset>
      </form>
      {status === "dialog-ready" && (
        <footer>
          <figure>
            <audio controls preload="none">
              <source src={podcastEndpoint} type="audio/mpeg" />
              <track
                kind="captions"
                src=""
                label="English captions"
                srcLang="en"
                default
              />
            </audio>
            <br />
          </figure>
        </footer>
      )}
    </article>
  );
}

export const ServerPodcastGeneratorCard: FC = ({
  dialogAction = "/api/dialog",
  podcastEndpoint = "/api/dialog-podcast",
}) => {
  const c = useRequestContext();
  const defaultLink =
    "https://developers.redhat.com/articles/2021/11/23/how-design-state-machines-microservices";
  const link = c.req.query("link") || defaultLink;
  const dialogReady = c.req.path.includes("/dialog");
  const status = dialogReady ? "dialog-ready" : "idle";

  return (
    <PodcastGeneratorCard
      dialogAction={dialogAction}
      podcastEndpoint={podcastEndpoint}
      link={link}
      status={status}
    />
  );
};

export function ClientPodcastGeneratorCard() {
  const defaultLink =
    "https://developers.redhat.com/articles/2021/11/23/how-design-state-machines-microservices";
  return (
    <PodcastGeneratorCard
      link={defaultLink}
      dialogAction="/api/v1/dialog"
      podcastEndpoint="/api/v1/dialog-podcast"
    />
  );
}
